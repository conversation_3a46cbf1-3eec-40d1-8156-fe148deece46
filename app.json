{"expo": {"name": "myuse", "slug": "myuse", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "usesAppleSignIn": true, "bundleIdentifier": "app.myuse.world", "googleServicesFile": "./google-service/GoogleService-Info.plist", "appleTeamId": "9445NHCXCU"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-service/google-services.json", "package": "app.myuse.world"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-apple-authentication", "expo-router", ["expo-font", {"fonts": ["./assets/fonts/Mukta-<PERSON>aani-Font/Mukta<PERSON>aani-Regular.ttf"]}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], "expo-secure-store", "@react-native-google-signin/google-signin", "expo-web-browser"], "experiments": {"typedRoutes": true}, "productionUrl": "wss://dev.myuse.world/websocket", "localUrl": "wss://dev.myuse.world/websocket"}}