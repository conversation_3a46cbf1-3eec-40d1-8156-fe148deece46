import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';

// Define a type for the response
type VerifyCodeResponse = {
  success: boolean;
  message?: string;
  data: {
    token: string;
  };
};

type SignUpResponse = {
  success: boolean;
  data: {
    token: string;
  };
};

type SignInResponse = {
  success: boolean;
};

type SignInWithAppleResponse = {
  success: boolean;
  data: {
    token: string;
  };
};

type ProfileResponse = {
  success: boolean;
  data: {
    preferences: any;
    profile: any;
  };
};

type UpdateUserProfileResponse = {
  success: boolean;
  [key: string]: any;
};

export const getProfile = () =>
  useQuery({
    queryKey: ['profile'],
    queryFn: () => {
      return new Promise<ProfileResponse>((resolve, reject) => {
        Meteor.call('users-getUser', (err: any, res: ProfileResponse) => {
          if (err) {
            console.log(err);
            reject(err);
            return;
          }

          console.log(res);
          resolve(res);
          return;
        });
      });
    },
  });

export const emailVerification = () =>
  useMutation({
    mutationFn: (email: string) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'auth-sendEmailVerification',
          { email },
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            } else {
              console.log(res);
              resolve(res);
              return;
            }
          },
        );
      });
    },
  });

export const verifyCode = () =>
  useMutation({
    mutationFn: ({ code, email }: { code: string; email: string }) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'auth-verifyCode',
          { code: Number(code), email },
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const signUp = () =>
  useMutation<SignUpResponse, Error, { email: string; password: string }>({
    mutationFn: (user) => {
      return new Promise<SignUpResponse>((resolve, reject) => {
        Meteor.call(
          'auth-signup',
          { email: user.email, password: user.password },
          (err: any, res: SignUpResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const signIn = () =>
  useMutation({
    mutationFn: (user: { email: string; password: string }) => {
      return new Promise<SignInResponse>((resolve, reject) => {
        Meteor.loginWithPassword(
          user.email,
          user.password,
          (err: any, res: SignUpResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            resolve({
              success: true,
            });
            return;
          },
        );
      });
    },
  });

export const signInWithApple = () =>
  useMutation({
    mutationFn: (metadata: any) => {
      return new Promise<SignInWithAppleResponse>((resolve, reject) => {
        Meteor.call(
          'auth-signupWithApple',
          { metadata },
          (err: any, res: SignInWithAppleResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const updateUserProfile = () =>
  useMutation({
    mutationFn: (profile: any) => {
      return new Promise<UpdateUserProfileResponse>((resolve, reject) => {
        Meteor.call(
          'users-updateProfile',
          { ...profile },
          (err: any, res: UpdateUserProfileResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const updateUserPreferences = () =>
  useMutation({
    mutationFn: (preferences: any) => {
      return new Promise<UpdateUserProfileResponse>((resolve, reject) => {
        Meteor.call(
          'users-updatePreferences',
          { ...preferences },
          (err: any, res: UpdateUserProfileResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const getUserProfile = () =>
  useQuery({
    queryKey: ['userProfile'],
    queryFn: () => {
      return new Promise<ProfileResponse>((resolve, reject) => {
        Meteor.call('users-getUser', (err: any, res: ProfileResponse) => {
          if (err) {
            console.log(err);
            reject(err);
            return;
          }

          // Log user profile data including gender information
          console.log('User Profile Data:', res);

          // Check specifically for gender information
          if (res?.data?.profile?.gender) {
            console.log('🧑‍🤝‍🧑 USER GENDER:', res.data.profile.gender);
            console.log(`User is ${res.data.profile.gender === 'Male' ? 'MALE ♂️' : res.data.profile.gender === 'Female' ? 'FEMALE ♀️' : 'NON-BINARY/OTHER'}`);
          } else {
            console.log('⚠️ No gender information found for current user');
          }

          resolve(res);
          return;
        });
      });
    },
  });

export const deleteVerification = () =>
  useMutation({
    mutationFn: () => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'users-sendDeleteAccountVerification',
          {},
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            } else {
              console.log(res);
              resolve(res);
              return;
            }
          },
        );
      });
    },
  });

export const userDeleteAccount = () =>
  useMutation({
    mutationFn: ({ code }: { code: string }) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'users-deleteAccount',
          { code: parseInt(code) },
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const sendForgotPasswordEmail = () =>
  useMutation({
    mutationFn: (email: string) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call('auth-forgotPassword', { email }, (err: any, res: VerifyCodeResponse) => {
          if (err) {
            console.log(err);
            reject(err);
            return;
          }
          console.log(res);
          resolve(res);
          return;
        });
      });
    },
  });

export const verifyForgotPasswordCode = () =>
  useMutation({
    mutationFn: ({ email, code }: { email: string, code: string; }) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'auth-verifyForgotPasswordCode',
          { email, code: Number(code) },
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const setNewPasswordViaEmail = () =>
  useMutation({
    mutationFn: ({ email, password }: { email: string; password: string }) => {
      return new Promise<VerifyCodeResponse>((resolve, reject) => {
        Meteor.call(
          'auth-setNewPasswordViaEmail',
          { email, newPassword: password },
          (err: any, res: VerifyCodeResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }
            console.log(res);
            resolve(res);
            return;
          },
        );
      });
    },
  });
