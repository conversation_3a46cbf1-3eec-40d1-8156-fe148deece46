import React, { ReactNode } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.2; // Slightly less tall than Style Diary

interface CollageCanvasProps {
  children: ReactNode;
}

export const CollageCanvas = ({ children }: CollageCanvasProps) => {
  return (
    <View style={styles.canvasContainer}>
      <View style={styles.canvas}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  canvasContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    marginVertical: 20,
  },
  canvas: {
    width: CANVAS_WIDTH,
    height: CANVAS_HEIGHT,
    backgroundColor: '#f8f8f8',
    position: 'relative',
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    borderStyle: 'dashed',
  },
});
