import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { isTablet } from '@/constants/responsive';

interface CollageControlsProps {
  onBringForward: () => void;
  onSendBackward: () => void;
  onDelete: () => void;
  disabled: boolean;
}

export const CollageControls = ({
  onBringForward,
  onSendBackward,
  onDelete,
  disabled,
}: CollageControlsProps) => {

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, disabled && styles.disabledButton]}
        onPress={onBringForward}
        disabled={disabled}
      >
        <Text style={[styles.buttonText, disabled && styles.disabledText]}>
          Bring Forward
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, disabled && styles.disabledButton]}
        onPress={onSendBackward}
        disabled={disabled}
      >
        <Text style={[styles.buttonText, disabled && styles.disabledText]}>
          Send Back
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.deleteButton, disabled && styles.disabledButton]}
        onPress={onDelete}
        disabled={disabled}
      >
        <Text style={[styles.deleteButtonText, disabled && styles.disabledText]}>
          Remove
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 10,
  },
  button: {
    paddingVertical: isTablet() ? 10 : 8,
    paddingHorizontal: isTablet() ? 16 : 12,
    backgroundColor: '#0E7E61',
    borderRadius: 8,
    minWidth: isTablet() ? 100 : 80,
    alignItems: 'center',
  },
  deleteButton: {
    paddingVertical: isTablet() ? 10 : 8,
    paddingHorizontal: isTablet() ? 16 : 12,
    backgroundColor: '#dc3545',
    borderRadius: 8,
    minWidth: isTablet() ? 100 : 80,
    alignItems: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontFamily: 'MuktaVaani',
    fontWeight: '500',
    fontSize: isTablet() ? 14 : 12,
  },
  deleteButtonText: {
    color: '#ffffff',
    fontFamily: 'MuktaVaani',
    fontWeight: '500',
    fontSize: isTablet() ? 14 : 12,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  disabledText: {
    color: '#888888',
  },
});
