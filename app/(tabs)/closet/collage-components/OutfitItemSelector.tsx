import React from 'react';
import { View, ScrollView, TouchableOpacity, Image, Text, StyleSheet } from 'react-native';
import { isTablet } from '@/constants/responsive';

interface OutfitItem {
  _id: string;
  name: string;
  imageUrl?: string;
  category?: string;
}

interface OutfitItemSelectorProps {
  items: OutfitItem[];
  onSelectItem: (item: OutfitItem) => void;
  selectedItems: string[]; // IDs of items already in the collage
}

export const OutfitItemSelector = ({
  items,
  onSelectItem,
  selectedItems,
}: OutfitItemSelectorProps) => {
  const isTabletDevice = isTablet();

  // Filter out items that are already in the collage
  const availableItems = items.filter(item => !selectedItems.includes(item._id));

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Add Items to Collage</Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {availableItems.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>All items are already in the collage</Text>
          </View>
        ) : (
          availableItems.map((item) => (
            <TouchableOpacity
              key={item._id}
              style={styles.itemContainer}
              onPress={() => onSelectItem(item)}
            >
              <Image
                source={
                  item.imageUrl
                    ? { uri: item.imageUrl }
                    : require('../../../../assets/images/placeholder-item.png')
                }
                style={styles.itemImage}
                resizeMode="cover"
              />
              <Text style={styles.itemName} numberOfLines={2}>
                {item.name}
              </Text>
            </TouchableOpacity>
          ))
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  sectionTitle: {
    fontFamily: 'MuktaVaani',
    fontWeight: '600',
    fontSize: isTablet() ? 18 : 16,
    color: '#333333',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  scrollView: {
    paddingLeft: 20,
  },
  scrollContent: {
    paddingRight: 20,
    gap: 12,
  },
  itemContainer: {
    width: isTablet() ? 90 : 70,
    alignItems: 'center',
  },
  itemImage: {
    width: isTablet() ? 80 : 60,
    height: isTablet() ? 80 : 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    marginBottom: 6,
  },
  itemName: {
    fontFamily: 'MuktaVaani',
    fontSize: isTablet() ? 12 : 10,
    color: '#666666',
    textAlign: 'center',
    lineHeight: isTablet() ? 14 : 12,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontFamily: 'MuktaVaani',
    fontSize: isTablet() ? 14 : 12,
    color: '#999999',
    textAlign: 'center',
  },
});
