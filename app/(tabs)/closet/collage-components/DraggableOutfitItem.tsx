import React from 'react';
import { Image, StyleSheet, TouchableOpacity } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import { PanGestureHandler } from 'react-native-gesture-handler';

interface DraggableOutfitItemProps {
  imageUrl: string;
  itemId: string;
  initialX?: number;
  initialY?: number;
  initialSize?: number;
  onPositionChange?: (itemId: string, position: { x: number; y: number }) => void;
  onSelect?: (itemId: string) => void;
  isSelected?: boolean;
}

export const DraggableOutfitItem = ({
  imageUrl,
  itemId,
  initialX = 50,
  initialY = 50,
  initialSize = 80,
  onPositionChange,
  onSelect,
  isSelected = false,
}: DraggableOutfitItemProps) => {
  const translateX = useSharedValue(initialX);
  const translateY = useSharedValue(initialY);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  });

  const panGestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      'worklet';
      context.startX = translateX.value;
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      'worklet';
      translateX.value = context.startX + event.translationX;
      translateY.value = context.startY + event.translationY;
    },
    onEnd: () => {
      'worklet';
      translateX.value = withSpring(translateX.value);
      translateY.value = withSpring(translateY.value);

      if (onPositionChange) {
        runOnJS(onPositionChange)(itemId, {
          x: translateX.value,
          y: translateY.value,
        });
      }
    },
  });

  const handleSelect = () => {
    if (onSelect) {
      onSelect(itemId);
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        animatedStyle,
        { width: initialSize, height: initialSize },
        isSelected && styles.selected,
      ]}
    >
      <PanGestureHandler onGestureEvent={panGestureHandler}>
        <Animated.View style={styles.dragArea}>
          <TouchableOpacity onPress={handleSelect} style={styles.touchable}>
            <Image
              source={{ uri: imageUrl }}
              defaultSource={require('../../../../assets/images/placeholder-item.png')}
              style={styles.image}
              resizeMode="cover"
            />
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    borderRadius: 8,
    overflow: 'hidden',
  },
  selected: {
    borderWidth: 2,
    borderColor: '#0E7E61',
    shadowColor: '#0E7E61',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  dragArea: {
    width: '100%',
    height: '100%',
  },
  touchable: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 6,
  },
});
