import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Modal from 'react-native-modal';

interface CollageEditOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (outfitData: any) => void;
  editMode?: boolean;
  existingOutfit?: any;
}

export const CollageEditOutfitModal = ({
  isVisible,
  onClose,
  onSave,
  editMode = false,
  existingOutfit,
}: CollageEditOutfitModalProps) => {
  console.log('CollageEditOutfitModal render - isVisible:', isVisible);
  console.log('CollageEditOutfitModal render - editMode:', editMode);
  console.log('CollageEditOutfitModal render - existingOutfit:', existingOutfit?.name);

  // Simple test - just return a basic modal first
  console.log('About to render Modal with isVisible:', isVisible);

  // If not visible, don't render anything
  if (!isVisible) {
    console.log('Modal not visible, returning null');
    return null;
  }

  console.log('Rendering modal content...');

  return (
    <Modal
      style={{ justifyContent: 'center', margin: 20 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      useNativeDriver={true}
    >
      <View style={{
        backgroundColor: 'white',
        minHeight: 300,
        maxHeight: 500,
        padding: 20,
        borderRadius: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 4,
        elevation: 5,
      }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 }}>
          <TouchableOpacity onPress={onClose}>
            <Text style={{ color: '#0E7E61', fontSize: 16 }}>Cancel</Text>
          </TouchableOpacity>
          <Text style={{ fontSize: 18, fontWeight: 'bold' }}>Collage Edit (Test)</Text>
          <TouchableOpacity onPress={onClose}>
            <Text style={{ color: '#0E7E61', fontSize: 16 }}>Save</Text>
          </TouchableOpacity>
        </View>

        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <Text style={{ fontSize: 16, textAlign: 'center', marginBottom: 20 }}>
            🎉 Collage Edit Modal is Working! 🎉
          </Text>
          <Text style={{ fontSize: 14, color: '#666', textAlign: 'center', marginBottom: 10 }}>
            Edit Mode: {editMode ? 'Yes' : 'No'}
          </Text>
          <Text style={{ fontSize: 14, color: '#666', textAlign: 'center', marginBottom: 10 }}>
            Outfit: {existingOutfit?.name || 'New Outfit'}
          </Text>
          <Text style={{ fontSize: 12, color: '#999', textAlign: 'center', marginTop: 20 }}>
            This is a test version. The full collage functionality will be added once this basic modal works without freezing.
          </Text>
        </View>
      </View>
    </Modal>
  );
};