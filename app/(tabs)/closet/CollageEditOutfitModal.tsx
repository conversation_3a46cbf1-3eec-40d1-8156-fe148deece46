import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
} from 'react-native';

interface CollageEditOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export const CollageEditOutfitModal = ({
  isVisible,
  onClose,
}: CollageEditOutfitModalProps) => {
  console.log('🎨 CollageEditOutfitModal - Step 1 - Render called');
  console.log('🎨 CollageEditOutfitModal - isVisible:', isVisible);
  
  if (!isVisible) {
    console.log('🎨 CollageEditOutfitModal - Not visible, returning null');
    return null;
  }

  console.log('🎨 CollageEditOutfitModal - Rendering modal content');

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        padding: 20,
      }}>
        <View style={{
          backgroundColor: 'white',
          borderRadius: 20,
          padding: 30,
          minWidth: 300,
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 4,
          elevation: 5,
        }}>
          <Text style={{
            fontSize: 24,
            fontWeight: 'bold',
            color: '#0E7E61',
            marginBottom: 20,
            textAlign: 'center',
          }}>
            🎨 Hello Collage! 🎨
          </Text>
          
          <Text style={{
            fontSize: 16,
            color: '#666',
            textAlign: 'center',
            marginBottom: 30,
            lineHeight: 22,
          }}>
            Step 1: Basic modal is working!{'\n'}
            This is our starting point for the collage feature.
          </Text>

          <TouchableOpacity
            onPress={() => {
              console.log('🎨 CollageEditOutfitModal - Close button pressed');
              onClose();
            }}
            style={{
              backgroundColor: '#0E7E61',
              paddingHorizontal: 30,
              paddingVertical: 12,
              borderRadius: 25,
            }}
          >
            <Text style={{
              color: 'white',
              fontSize: 16,
              fontWeight: '600',
            }}>
              Close
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};
