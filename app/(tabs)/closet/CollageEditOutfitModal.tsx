import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Image,
} from 'react-native';
import { getClothes } from '@/methods/cloths';
import DatePickerModal from '@/components/DatePickerModal';

interface CollageEditOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
  editMode?: boolean;
  existingOutfit?: any;
}

export const CollageEditOutfitModal = ({
  isVisible,
  onClose,
  editMode = false,
  existingOutfit,
}: CollageEditOutfitModalProps) => {
  console.log('🎨 CollageEditOutfitModal - Step 4 - Render called');
  console.log('🎨 CollageEditOutfitModal - isVisible:', isVisible);
  console.log('🎨 CollageEditOutfitModal - editMode:', editMode);
  console.log('🎨 CollageEditOutfitModal - existingOutfit FULL OBJECT:', JSON.stringify(existingOutfit, null, 2));

  // Debug outfit data structure
  if (existingOutfit) {
    console.log('🎨 CollageEditOutfitModal - Outfit properties:');
    console.log('🎨 - _id:', existingOutfit._id);
    console.log('🎨 - name:', existingOutfit.name);
    console.log('🎨 - itemIds:', existingOutfit.itemIds);
    console.log('🎨 - selectedItems:', existingOutfit.selectedItems);
    console.log('🎨 - items:', existingOutfit.items);
    console.log('🎨 - All keys:', Object.keys(existingOutfit));
  }

  // Form state
  const [outfitName, setOutfitName] = useState('');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());

  // Date picker state
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Canvas items state - tracks which items are currently in the canvas
  const [canvasItems, setCanvasItems] = useState<string[]>([]);

  // Item selection panel state
  const [showItemSelection, setShowItemSelection] = useState(false);

  // Populate form with existing outfit data
  useEffect(() => {
    if (editMode && existingOutfit && isVisible) {
      console.log('🎨 CollageEditOutfitModal - Step 5 - Populating form with existing outfit data');

      setOutfitName(existingOutfit.name || '');
      setLocation(existingOutfit.location || 'London, United Kingdom');

      // Handle date conversion
      if (existingOutfit.eventDate) {
        const date = existingOutfit.eventDate instanceof Date
          ? existingOutfit.eventDate
          : new Date(existingOutfit.eventDate);
        setEventDate(date);
      }

      // Populate canvas items from existing outfit
      const outfitItemIds = existingOutfit?.selectedItems || [];
      setCanvasItems(outfitItemIds);

      console.log('🎨 CollageEditOutfitModal - Step 5 - Form populated with:', {
        name: existingOutfit.name,
        location: existingOutfit.location,
        eventDate: existingOutfit.eventDate,
        itemIds: existingOutfit.itemIds,
        selectedItems: existingOutfit.selectedItems,
        canvasItems: outfitItemIds,
      });
    } else if (!editMode) {
      // Reset form for new outfit
      console.log('🎨 CollageEditOutfitModal - Step 5 - Resetting form for new outfit');
      setOutfitName('');
      setLocation('London, United Kingdom');
      setEventDate(new Date());
      setCanvasItems([]); // Start with empty canvas for new outfits
    }
  }, [editMode, existingOutfit, isVisible]);

  // Fetch clothes data
  const { data: clothesData, isLoading: clothesLoading, error: clothesError } = getClothes();

  // Get clothes with fallback to existing outfit items
  const clothes = React.useMemo(() => {
    const loadedClothes = clothesData?.data || [];

    // If we have loaded clothes, use them
    if (loadedClothes.length > 0) {
      return loadedClothes;
    }

    // Fallback: if we're in edit mode and have outfit items, use those
    if (editMode && existingOutfit?.items && Array.isArray(existingOutfit.items)) {
      console.log('🎨 CollageEditOutfitModal - Using existingOutfit.items as fallback for clothes');
      return existingOutfit.items;
    }

    return [];
  }, [clothesData, editMode, existingOutfit]);

  // Debug clothes loading
  console.log('🎨 CollageEditOutfitModal - Clothes loading debug:');
  console.log('🎨 - clothesLoading:', clothesLoading);
  console.log('🎨 - clothesError:', clothesError);
  console.log('🎨 - clothesData:', clothesData);
  console.log('🎨 - clothesData?.data:', clothesData?.data);
  console.log('🎨 - clothes.length:', clothes.length);
  if (clothes.length > 0) {
    console.log('🎨 - First few clothes:', clothes.slice(0, 3).map((item: any) => ({
      id: item._id,
      name: item.name,
    })));
  }

  // Get items to display in canvas based on canvasItems state
  const displayItems = React.useMemo(() => {
    console.log('🎨 CollageEditOutfitModal - Step 5 - Getting canvas items to display');
    console.log('🎨 CollageEditOutfitModal - canvasItems:', canvasItems);
    console.log('🎨 CollageEditOutfitModal - clothes.length:', clothes.length);

    if (canvasItems.length > 0) {
      // Method 1: Try to get items from existingOutfit.items first (full objects)
      if (editMode && existingOutfit?.items && Array.isArray(existingOutfit.items)) {
        const canvasItemsFromOutfit = existingOutfit.items.filter((item: any) =>
          canvasItems.includes(item._id)
        );

        if (canvasItemsFromOutfit.length > 0) {
          console.log('🎨 CollageEditOutfitModal - Using items from existingOutfit.items:', {
            canvasItemsCount: canvasItemsFromOutfit.length,
            items: canvasItemsFromOutfit.map((item: any) => ({ id: item._id, name: item.name })),
          });
          return canvasItemsFromOutfit;
        }
      }

      // Method 2: Fallback to matching with loaded clothes
      const canvasItemsFromClothes = clothes.filter((item: any) =>
        canvasItems.includes(item._id)
      );

      console.log('🎨 CollageEditOutfitModal - Using items from clothes collection:', {
        canvasItemsCount: canvasItemsFromClothes.length,
        totalClothes: clothes.length,
        items: canvasItemsFromClothes.map((item: any) => ({ id: item._id, name: item.name })),
      });

      return canvasItemsFromClothes;
    } else {
      // Empty canvas
      console.log('🎨 CollageEditOutfitModal - Step 5 - Empty canvas');
      return [];
    }
  }, [canvasItems, existingOutfit, clothes, editMode]);

  console.log('🎨 CollageEditOutfitModal - Step 4 - Form state:', {
    outfitName,
    location,
    eventDate: eventDate.toDateString(),
    editMode,
    existingOutfitName: existingOutfit?.name,
  });

  console.log('🎨 CollageEditOutfitModal - Step 4 - Clothes data:', {
    clothesLoading,
    clothesCount: clothes.length,
    displayItemsCount: displayItems.length,
    isEditMode: editMode,
  });

  if (!isVisible) {
    console.log('🎨 CollageEditOutfitModal - Not visible, returning null');
    return null;
  }

  console.log('🎨 CollageEditOutfitModal - Rendering modal content');

  // Format date for display
  const formatDate = (date: Date): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  return (
    <Modal
      visible={isVisible}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <View style={{
        flex: 1,
        backgroundColor: 'white',
      }}>
        <View style={{
          flex: 1,
          paddingTop: 50, // Safe area padding for status bar
        }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingVertical: 15,
            backgroundColor: '#f8f8f8',
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0',
            marginBottom: 0,
          }}>
            <TouchableOpacity onPress={onClose}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>Cancel</Text>
            </TouchableOpacity>
            <Text style={{
              fontSize: 18,
              fontWeight: 'bold',
              color: '#333',
            }}>
              {editMode ? 'Edit Outfit (Collage)' : 'Create Outfit (Collage)'}
            </Text>
            <TouchableOpacity onPress={() => {
              console.log('🎨 CollageEditOutfitModal - Step 4 - Update button pressed');
              console.log('🎨 CollageEditOutfitModal - Step 4 - Form data:', { outfitName, location, eventDate });
            }}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>
                {editMode ? 'Update' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={{ flex: 1 }}
            contentContainerStyle={{ padding: 20 }}
            showsVerticalScrollIndicator={false}
          >


            {/* Outfit Name */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Outfit Name
              </Text>
              <TextInput
                value={outfitName}
                onChangeText={(text) => {
                  console.log('🎨 CollageEditOutfitModal - Step 2 - Outfit name changed:', text);
                  setOutfitName(text);
                }}
                placeholder="Enter outfit name"
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#f8f8f8',
                }}
              />
            </View>

            {/* Location */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Location
              </Text>
              <TextInput
                value={location}
                onChangeText={(text) => {
                  console.log('🎨 CollageEditOutfitModal - Step 2 - Location changed:', text);
                  setLocation(text);
                }}
                placeholder="Enter location"
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  fontSize: 16,
                  backgroundColor: '#f8f8f8',
                }}
              />
            </View>

            {/* Event Date */}
            <View style={{ marginBottom: 20 }}>
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: '#333',
                marginBottom: 8,
              }}>
                Event Date
              </Text>
              <TouchableOpacity
                onPress={() => {
                  console.log('🎨 CollageEditOutfitModal - Opening date picker');
                  setShowDatePicker(true);
                }}
                style={{
                  borderWidth: 1,
                  borderColor: '#e0e0e0',
                  borderRadius: 8,
                  padding: 12,
                  backgroundColor: '#f8f8f8',
                }}
              >
                <Text style={{ fontSize: 16, color: '#333' }}>
                  {formatDate(eventDate)}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Canvas with Static Items */}
            <View style={{
              backgroundColor: '#f8f8f8',
              borderWidth: 2,
              borderColor: '#e0e0e0',
              borderStyle: 'dashed',
              borderRadius: 12,
              minHeight: 400,
              padding: 20,
              marginBottom: 20,
            }}>
              <View style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 15,
              }}>
                <Text style={{
                  fontSize: 16,
                  color: '#0E7E61',
                  fontWeight: '600',
                }}>
                  Canvas Area ({displayItems.length} items)
                </Text>
                <TouchableOpacity
                  onPress={() => {
                    console.log('🎨 CollageEditOutfitModal - Step 5 - Add Items button pressed');
                    setShowItemSelection(true);
                  }}
                  style={{
                    backgroundColor: '#0E7E61',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 15,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  <Text style={{
                    color: 'white',
                    fontSize: 12,
                    fontWeight: '600',
                    marginRight: 4,
                  }}>
                    Add Items
                  </Text>
                  <Text style={{ color: 'white', fontSize: 14 }}>+</Text>
                </TouchableOpacity>
              </View>

              {clothesLoading ? (
                <View style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 200,
                }}>
                  <Text style={{ color: '#999', fontSize: 16 }}>Loading clothes...</Text>
                </View>
              ) : displayItems.length === 0 ? (
                <View style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 200,
                }}>
                  <Text style={{ color: '#999', fontSize: 16, textAlign: 'center' }}>
                    {editMode
                      ? `No items found for this outfit.{'\n'}The outfit might have been created with items that no longer exist.{'\n\n'}Outfit ID: ${existingOutfit?._id}{'\n'}Expected ${existingOutfit?.itemIds?.length || 0} items`
                      : `No clothes found.{'\n'}Add some clothes to your closet first!`
                    }
                  </Text>
                </View>
              ) : (
                <View style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  justifyContent: 'space-around',
                  alignItems: 'flex-start',
                  gap: 15,
                }}>
                  {displayItems.map((item: any, index: number) => (
                    <TouchableOpacity
                      key={item._id || `item-${index}`}
                      onPress={() => {
                        console.log('🎨 CollageEditOutfitModal - Step 5 - Removing item from canvas:', item.name);
                        // Remove item from canvas
                        setCanvasItems(prev => prev.filter(id => id !== item._id));
                      }}
                      style={{
                        backgroundColor: 'white',
                        borderRadius: 8,
                        padding: 8,
                        shadowColor: '#000',
                        shadowOffset: { width: 0, height: 2 },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                        elevation: 3,
                        width: 80,
                        alignItems: 'center',
                        position: 'relative',
                      }}
                    >
                      {/* Remove indicator */}
                      <View style={{
                        position: 'absolute',
                        top: -5,
                        right: -5,
                        backgroundColor: '#ff4444',
                        borderRadius: 10,
                        width: 20,
                        height: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1,
                      }}>
                        <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>×</Text>
                      </View>
                      <Image
                        source={
                          item.imageUrl
                            ? { uri: item.imageUrl }
                            : require('../../../assets/images/placeholder-item.png')
                        }
                        style={{
                          width: 60,
                          height: 60,
                          borderRadius: 6,
                          backgroundColor: '#f0f0f0',
                        }}
                        resizeMode="cover"
                      />
                      <Text
                        style={{
                          fontSize: 10,
                          color: '#333',
                          textAlign: 'center',
                          marginTop: 4,
                          fontWeight: '500',
                        }}
                        numberOfLines={2}
                      >
                        {item.name || `Item ${index + 1}`}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}

              {!editMode && clothes.length > 8 && (
                <Text style={{
                  fontSize: 12,
                  color: '#999',
                  textAlign: 'center',
                  marginTop: 15,
                  fontStyle: 'italic',
                }}>
                  Showing first 8 items (Step 5 will add item selection)
                </Text>
              )}
            </View>
          </ScrollView>
        </View>
      </View>

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => {
          console.log('🎨 CollageEditOutfitModal - Date picker closed');
          setShowDatePicker(false);
        }}
        onDateChange={(date) => {
          if (date) {
            console.log('🎨 CollageEditOutfitModal - Date selected:', date);
            setEventDate(date);
          }
        }}
        value={eventDate}
        title="Select Event Date"
      />

      {/* Item Selection Modal */}
      <Modal
        visible={showItemSelection}
        transparent={false}
        animationType="slide"
        onRequestClose={() => setShowItemSelection(false)}
        presentationStyle="fullScreen"
      >
        <View style={{ flex: 1, backgroundColor: 'white', paddingTop: 50 }}>
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingVertical: 15,
            backgroundColor: '#f8f8f8',
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0',
          }}>
            <TouchableOpacity onPress={() => setShowItemSelection(false)}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>Cancel</Text>
            </TouchableOpacity>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#333' }}>
              Add Items to Canvas
            </Text>
            <TouchableOpacity onPress={() => setShowItemSelection(false)}>
              <Text style={{ color: '#0E7E61', fontSize: 16, fontWeight: '500' }}>Done</Text>
            </TouchableOpacity>
          </View>

          {/* Available Items Grid */}
          <ScrollView style={{ flex: 1, padding: 20 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#333',
              marginBottom: 15,
            }}>
              Available Items ({clothes.length} total)
            </Text>

            {/* Debug info */}
            <View style={{
              backgroundColor: '#f0f9f7',
              padding: 10,
              borderRadius: 8,
              marginBottom: 15,
            }}>
              <Text style={{ fontSize: 12, color: '#0E7E61' }}>
                🔍 Debug: clothesLoading={clothesLoading ? 'true' : 'false'}, clothes.length={clothes.length}
              </Text>
              {clothesData && (
                <Text style={{ fontSize: 12, color: '#0E7E61' }}>
                  clothesData.data exists: {clothesData.data ? 'yes' : 'no'}
                </Text>
              )}
            </View>

            {clothesLoading ? (
              <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: 200,
              }}>
                <Text style={{ color: '#999', fontSize: 16 }}>Loading clothes...</Text>
              </View>
            ) : clothes.length === 0 ? (
              <View style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: 200,
              }}>
                <Text style={{ color: '#999', fontSize: 16, textAlign: 'center' }}>
                  No clothes found.{'\n'}
                  Add some clothes to your closet first!
                </Text>
              </View>
            ) : (
              <View style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                justifyContent: 'space-around',
                gap: 15,
              }}>
                {clothes.map((item: any, index: number) => {
                const isInCanvas = canvasItems.includes(item._id);
                return (
                  <TouchableOpacity
                    key={item._id || `available-item-${index}`}
                    onPress={() => {
                      if (isInCanvas) {
                        console.log('🎨 CollageEditOutfitModal - Step 5 - Removing item from canvas:', item.name);
                        setCanvasItems(prev => prev.filter(id => id !== item._id));
                      } else {
                        console.log('🎨 CollageEditOutfitModal - Step 5 - Adding item to canvas:', item.name);
                        setCanvasItems(prev => [...prev, item._id]);
                      }
                    }}
                    style={{
                      backgroundColor: isInCanvas ? '#e8f5f0' : 'white',
                      borderRadius: 8,
                      padding: 8,
                      shadowColor: '#000',
                      shadowOffset: { width: 0, height: 2 },
                      shadowOpacity: 0.1,
                      shadowRadius: 4,
                      elevation: 3,
                      width: 80,
                      alignItems: 'center',
                      borderWidth: isInCanvas ? 2 : 1,
                      borderColor: isInCanvas ? '#0E7E61' : '#e0e0e0',
                    }}
                  >
                    {/* Selection indicator */}
                    {isInCanvas && (
                      <View style={{
                        position: 'absolute',
                        top: -5,
                        right: -5,
                        backgroundColor: '#0E7E61',
                        borderRadius: 10,
                        width: 20,
                        height: 20,
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 1,
                      }}>
                        <Text style={{ color: 'white', fontSize: 12, fontWeight: 'bold' }}>✓</Text>
                      </View>
                    )}

                    <Image
                      source={
                        item.imageUrl
                          ? { uri: item.imageUrl }
                          : require('../../../assets/images/placeholder-item.png')
                      }
                      style={{
                        width: 60,
                        height: 60,
                        borderRadius: 6,
                        backgroundColor: '#f0f0f0',
                      }}
                      resizeMode="cover"
                    />
                    <Text
                      style={{
                        fontSize: 10,
                        color: '#333',
                        textAlign: 'center',
                        marginTop: 4,
                        fontWeight: '500',
                      }}
                      numberOfLines={2}
                    >
                      {item.name || `Item ${index + 1}`}
                    </Text>
                  </TouchableOpacity>
                );
              })}
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </Modal>
  );
};
