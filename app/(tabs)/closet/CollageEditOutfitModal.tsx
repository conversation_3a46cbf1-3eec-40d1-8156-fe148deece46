import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import Modal from 'react-native-modal';
import { useQuery } from '@tanstack/react-query';

// Import our custom collage components
import { CollageCanvas } from './collage-components/CollageCanvas';
import { DraggableOutfitItem } from './collage-components/DraggableOutfitItem';
import { CollageControls } from './collage-components/CollageControls';
import { OutfitItemSelector } from './collage-components/OutfitItemSelector';

// Import existing components and styles
import {
  Modal<PERSON>ontainer,
  ContentContainer,
  FrameContainer,
  FormGroup,
  InputFieldContainer,
  InputFieldLabel,
  StyledTextInput,
  AndroidLocationTextInput,
  DateButton,
  DateButtonText,
  ModalHeader,
  HeaderButton,
  HeaderButtonText,
  HeaderTitle,
} from './PlanOutfitModal.styles';

// Import existing hooks and utilities
import { createOutfit, updateOutfit } from '@/methods/outfits';
import { getClothes } from '@/methods/cloths';

interface CollageItem {
  id: string;
  itemId: string;
  imageUrl: string;
  x: number;
  y: number;
  size: number;
  zIndex: number;
}

interface CollageEditOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (outfitData: any) => void;
  editMode?: boolean;
  existingOutfit?: any;
}

export const CollageEditOutfitModal = ({
  isVisible,
  onClose,
  onSave,
  editMode = false,
  existingOutfit,
}: CollageEditOutfitModalProps) => {
  // Form states
  const [outfitName, setOutfitName] = useState('');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());

  // Collage states
  const [collageItems, setCollageItems] = useState<CollageItem[]>([]);
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null);
  const [nextZIndex, setNextZIndex] = useState(1);

  // API hooks
  const createOutfitMutation = createOutfit();
  const updateOutfitMutation = updateOutfit();
  const { data: clothesData } = useQuery({
    queryKey: ['clothes'],
    queryFn: getClothes,
  });

  const clothes = clothesData?.data || [];

  // Initialize form data when editing
  useEffect(() => {
    if (editMode && existingOutfit) {
      setOutfitName(existingOutfit.name || '');
      setLocation(existingOutfit.location || 'London, United Kingdom');
      
      const eventDate = existingOutfit.eventDate instanceof Date
        ? existingOutfit.eventDate
        : new Date(existingOutfit.eventDate);
      setEventDate(eventDate);

      // Initialize collage items from existing outfit
      if (existingOutfit.itemIds && Array.isArray(existingOutfit.itemIds)) {
        const initialCollageItems: CollageItem[] = existingOutfit.itemIds.map((itemId: string, index: number) => {
          const clothItem = clothes.find((item: any) => item._id === itemId);
          return {
            id: `${itemId}-${Date.now()}-${index}`,
            itemId,
            imageUrl: clothItem?.imageUrl || '',
            x: 50 + (index % 3) * 60, // Simple grid positioning
            y: 50 + Math.floor(index / 3) * 80,
            size: 80,
            zIndex: index + 1,
          };
        });
        setCollageItems(initialCollageItems);
        setNextZIndex(initialCollageItems.length + 1);
      }
    }
  }, [editMode, existingOutfit, clothes]);

  // Reset form
  const resetForm = useCallback(() => {
    setOutfitName('');
    setLocation('London, United Kingdom');
    setEventDate(new Date());
    setCollageItems([]);
    setSelectedItemIndex(null);
    setNextZIndex(1);
  }, []);

  // Add item to collage
  const handleAddItemToCollage = useCallback((item: any) => {
    const newCollageItem: CollageItem = {
      id: `${item._id}-${Date.now()}`,
      itemId: item._id,
      imageUrl: item.imageUrl || '',
      x: 50 + (collageItems.length % 3) * 60,
      y: 50 + Math.floor(collageItems.length / 3) * 80,
      size: 80,
      zIndex: nextZIndex,
    };

    setCollageItems(prev => [...prev, newCollageItem]);
    setNextZIndex(prev => prev + 1);
  }, [collageItems.length, nextZIndex]);

  // Handle item position change
  const handleItemPositionChange = useCallback((itemId: string, position: { x: number; y: number }) => {
    setCollageItems(prev =>
      prev.map(item =>
        item.id === itemId ? { ...item, ...position } : item
      )
    );
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    const index = collageItems.findIndex(item => item.id === itemId);
    setSelectedItemIndex(index >= 0 ? index : null);
  }, [collageItems]);

  // Bring item forward
  const handleBringForward = useCallback(() => {
    if (selectedItemIndex === null) return;

    setCollageItems(prev => {
      const newItems = [...prev];
      const selectedItem = newItems[selectedItemIndex];
      selectedItem.zIndex = Math.max(...newItems.map(item => item.zIndex)) + 1;
      return newItems;
    });
  }, [selectedItemIndex]);

  // Send item backward
  const handleSendBackward = useCallback(() => {
    if (selectedItemIndex === null) return;

    setCollageItems(prev => {
      const newItems = [...prev];
      const selectedItem = newItems[selectedItemIndex];
      selectedItem.zIndex = Math.max(1, Math.min(...newItems.map(item => item.zIndex)) - 1);
      return newItems;
    });
  }, [selectedItemIndex]);

  // Delete selected item
  const handleDeleteItem = useCallback(() => {
    if (selectedItemIndex === null) return;

    setCollageItems(prev => prev.filter((_, index) => index !== selectedItemIndex));
    setSelectedItemIndex(null);
  }, [selectedItemIndex]);

  // Handle save
  const handleSave = async () => {
    if (!outfitName.trim()) {
      Alert.alert('Missing Information', 'Please enter an outfit name.');
      return;
    }

    if (collageItems.length === 0) {
      Alert.alert('No items selected', 'Please add at least one clothing item to your outfit.');
      return;
    }

    try {
      const itemIds = collageItems.map(item => item.itemId);

      if (editMode && existingOutfit) {
        // Update existing outfit
        const updateParams = {
          outfitId: existingOutfit._id,
          name: outfitName,
          location: location,
          eventDate: eventDate,
          itemIds: itemIds,
          plannedDate: eventDate,
        };

        const result = await updateOutfitMutation.mutateAsync(updateParams);

        Alert.alert(
          'Success!',
          `Outfit "${outfitName}" has been updated with ${itemIds.length} items.`,
          [{ text: 'OK' }]
        );
      } else {
        // Create new outfit
        const createParams = {
          name: outfitName,
          location: location,
          eventDate: eventDate,
          itemIds: itemIds,
          plannedDate: eventDate,
        };

        const result = await createOutfitMutation.mutateAsync(createParams);

        Alert.alert(
          'Success!',
          `Outfit "${outfitName}" has been created with ${itemIds.length} items.`,
          [{ text: 'OK' }]
        );
      }

      // Reset form and close modal
      resetForm();
      onClose();

      // Call the original onSave callback
      const outfitData = {
        name: outfitName,
        location,
        eventDate,
        selectedItems: itemIds,
        collageUri: undefined,
      };
      onSave(outfitData);

    } catch (error) {
      console.error(`Error ${editMode ? 'updating' : 'creating'} outfit:`, error);
      Alert.alert(
        'Error',
        `Failed to ${editMode ? 'update' : 'create'} outfit. Please try again.`,
        [{ text: 'OK' }]
      );
    }
  };

  // Format date for display
  const formatDate = (date: Date): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <ModalContainer>
        <ModalHeader>
          <HeaderButton onPress={onClose}>
            <HeaderButtonText>Cancel</HeaderButtonText>
          </HeaderButton>
          <HeaderTitle>{editMode ? 'Edit Outfit (Collage)' : 'Create Outfit (Collage)'}</HeaderTitle>
          <HeaderButton
            onPress={handleSave}
            disabled={editMode ? updateOutfitMutation.isPending : createOutfitMutation.isPending}
          >
            {(editMode ? updateOutfitMutation.isPending : createOutfitMutation.isPending) ? (
              <ActivityIndicator size="small" color="#0E7E61" />
            ) : (
              <HeaderButtonText>{editMode ? 'Update' : 'Save'}</HeaderButtonText>
            )}
          </HeaderButton>
        </ModalHeader>

        <ContentContainer
          showsVerticalScrollIndicator={true}
          bounces={true}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
        >
          <FrameContainer>
            <FormGroup>
              {/* Outfit Name Input */}
              <InputFieldContainer>
                <InputFieldLabel>Outfit Name</InputFieldLabel>
                <StyledTextInput
                  value={outfitName}
                  onChangeText={setOutfitName}
                  placeholder="Enter outfit name"
                />
              </InputFieldContainer>

              {/* Location Input */}
              <InputFieldContainer>
                <InputFieldLabel>Location</InputFieldLabel>
                <AndroidLocationTextInput
                  value={location}
                  onChangeText={setLocation}
                  placeholder="Enter location"
                />
              </InputFieldContainer>

              {/* Event Date Input */}
              <InputFieldContainer>
                <InputFieldLabel>Event Date</InputFieldLabel>
                <DateButton onPress={() => {}}>
                  <DateButtonText>{formatDate(eventDate)}</DateButtonText>
                </DateButton>
              </InputFieldContainer>
            </FormGroup>

            {/* Collage Canvas */}
            <CollageCanvas>
              {collageItems
                .sort((a, b) => a.zIndex - b.zIndex) // Sort by z-index for proper layering
                .map((item, index) => {
                  const originalIndex = collageItems.findIndex(original => original.id === item.id);
                  return (
                    <DraggableOutfitItem
                      key={item.id}
                      imageUrl={item.imageUrl}
                      itemId={item.id}
                      initialX={item.x}
                      initialY={item.y}
                      initialSize={item.size}
                      onPositionChange={handleItemPositionChange}
                      onSelect={handleItemSelect}
                      isSelected={selectedItemIndex === originalIndex}
                    />
                  );
                })}
            </CollageCanvas>

            {/* Collage Controls */}
            <CollageControls
              onBringForward={handleBringForward}
              onSendBackward={handleSendBackward}
              onDelete={handleDeleteItem}
              disabled={selectedItemIndex === null}
            />
          </FrameContainer>
        </ContentContainer>

        {/* Item Selector */}
        <OutfitItemSelector
          items={clothes}
          onSelectItem={handleAddItemToCollage}
          selectedItems={collageItems.map(item => item.itemId)}
        />
      </ModalContainer>
    </Modal>
  );
};
