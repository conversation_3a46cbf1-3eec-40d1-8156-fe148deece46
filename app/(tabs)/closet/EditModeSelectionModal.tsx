import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';
import { isTablet } from '@/constants/responsive';

interface EditModeSelectionModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectRegularEdit: () => void;
  onSelectCollageEdit: () => void;
  outfitName?: string;
}

export const EditModeSelectionModal = ({
  isVisible,
  onClose,
  onSelectRegularEdit,
  onSelectCollageEdit,
  outfitName,
}: EditModeSelectionModalProps) => {
  const isTabletDevice = isTablet();

  return (
    <Modal
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      style={styles.modal}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Choose Edit Mode</Text>
          <Text style={styles.subtitle}>
            How would you like to edit "{outfitName}"?
          </Text>
        </View>

        <View style={styles.optionsContainer}>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={onSelectRegularEdit}
          >
            <Text style={styles.optionTitle}>Regular Edit</Text>
            <Text style={styles.optionDescription}>
              Edit outfit details and select items with checkboxes
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.optionButton, styles.collageOption]}
            onPress={onSelectCollageEdit}
          >
            <Text style={[styles.optionTitle, styles.collageTitle]}>Collage Edit</Text>
            <Text style={[styles.optionDescription, styles.collageDescription]}>
              Arrange items visually in a collage layout
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    margin: 20,
  },
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: isTablet() ? 32 : 24,
    width: '100%',
    maxWidth: isTablet() ? 400 : 320,
  },
  header: {
    alignItems: 'center',
    marginBottom: isTablet() ? 32 : 24,
  },
  title: {
    fontFamily: 'MuktaVaani',
    fontWeight: '600',
    fontSize: isTablet() ? 22 : 20,
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontFamily: 'MuktaVaani',
    fontSize: isTablet() ? 16 : 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: isTablet() ? 22 : 20,
  },
  optionsContainer: {
    gap: isTablet() ? 16 : 12,
    marginBottom: isTablet() ? 32 : 24,
  },
  optionButton: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: isTablet() ? 20 : 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  collageOption: {
    backgroundColor: '#f0f9f7',
    borderColor: '#0E7E61',
  },
  optionTitle: {
    fontFamily: 'MuktaVaani',
    fontWeight: '600',
    fontSize: isTablet() ? 18 : 16,
    color: '#333333',
    marginBottom: 6,
  },
  collageTitle: {
    color: '#0E7E61',
  },
  optionDescription: {
    fontFamily: 'MuktaVaani',
    fontSize: isTablet() ? 14 : 12,
    color: '#666666',
    lineHeight: isTablet() ? 18 : 16,
  },
  collageDescription: {
    color: '#0E7E61',
  },
  cancelButton: {
    alignItems: 'center',
    paddingVertical: isTablet() ? 16 : 12,
  },
  cancelText: {
    fontFamily: 'MuktaVaani',
    fontWeight: '500',
    fontSize: isTablet() ? 16 : 14,
    color: '#999999',
  },
});
