{"name": "myuse", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start -c", "android": "DARK_MODE=media expo run:android", "ios": "expo run:ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "0.4.2", "@expo/vector-icons": "^14.0.2", "@gluestack-ui/actionsheet": "^0.2.52", "@gluestack-ui/alert-dialog": "^0.1.38", "@gluestack-ui/icon": "^0.1.26", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@gorhom/bottom-sheet": "^5", "@hookform/resolvers": "^3.10.0", "@legendapp/motion": "^2.4.0", "@meteorrn/core": "^2.8.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/native": "^7.0.14", "@shopify/react-native-skia": "v2.0.0-next.4", "@tanstack/react-query": "^5.53.1", "babel-plugin-module-resolver": "^5.0.2", "base-64": "^1.0.0", "ddp-client": "^0.1.2", "expo": "^53.0.9", "expo-apple-authentication": "~7.2.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "^14.0.1", "expo-image": "~2.1.7", "expo-image-picker": "^16.0.6", "expo-linear-gradient": "^14.1.4", "expo-linking": "~7.1.5", "expo-local-authentication": "~16.0.4", "expo-media-library": "~17.1.6", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "^14.1.6", "lucide-react-native": "^0.469.0", "moment": "^2.30.1", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-native": "0.79.2", "react-native-autocomplete-input": "^5.5.6", "react-native-css-interop": "^0.1.22", "react-native-draggable-flatlist": "^4.0.1", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-ui-datepicker": "^3.1.2", "react-native-uuid": "^2.0.3", "react-native-view-shot": "~4.0.3", "react-native-web": "^0.20.0", "styled-components": "^6.1.14", "tailwindcss": "3.4.17", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "4", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/meteor": "^2.9.8", "@types/react": "~19.0.10", "@types/styled-components-react-native": "^5.2.5", "jest": "^29.2.1", "jest-expo": "~53.0.5", "jscodeshift": "0.15.2", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "~5.8.3"}, "private": true, "packageManager": "yarn@4.9.1"}