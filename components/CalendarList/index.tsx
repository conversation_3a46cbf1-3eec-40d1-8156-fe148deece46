import React, { useState, useCallback, useRef } from 'react';
import {
  Calendar<PERSON>istContainer,
  WhiteBackDrop,
  DayItem, DayItemContent, DayItemText, DayItemTextSmall,
  CalendarContent,
  CalendarContentTitle,
  ClothsItemContainer,
  ClothsItem,
  PaginationContainer,
  PaginationDot,
  ComingSoonText
} from './styles';
import moment from 'moment';
import { FlatList, View, Dimensions, TouchableOpacity } from 'react-native';
import {
  Blend,
  Canvas,
  Color,
  LinearGradient,
  Rect,
  vec,
} from '@shopify/react-native-skia';
import { Pressable } from 'react-native';

import Sunny from '@/assets/svg/sunny.svg';
import Cloudy from '@/assets/svg/cloudy.svg';
import Rainy from '@/assets/svg/rainy.svg';
import PartlyCloudy from '@/assets/svg/day-cloud.svg';
import PartlySunny from '@/assets/svg/day-cloud-high.svg';

import LuggageSvg from '@/assets/svg/luggage.svg';
import LuggageEmptySvg from '@/assets/svg/luggage-empty.svg';
import { getUpcomingTrips } from '@/methods/trips';
import { useRouter   } from 'expo-router';

// Update days array to show exactly 28 days (4 weeks)
const days = Array.from({ length: 28 }, (_, index) => {
  return moment().startOf('week').add(index, 'days');
});

// Update weather data to match new days array
const weatherData = Array.from({ length: 28 }, () => ({
  temperature: Math.floor(Math.random() * 30) + 10,
  condition: ['sunny', 'cloudy', 'rainy', 'partly cloudy'][Math.floor(Math.random() * 4)]
}));

//can you assign gradient colors based on the weather data
const gradientColors = weatherData.map((weather) => {
  if (weather.condition === 'sunny') {
    return ['#FFFFFF', '#E9631A'];
  }
  if (weather.condition === 'cloudy') {
    return ['#BCD7EA', '#A8A8A8'];
  }
  if (weather.condition === 'rainy') {
    return ['#7AC5FB', '#57636A'];
  }
  if (weather.condition === 'partly cloudy') {
    return ['#FFFFFF', '#E9631A'];
  }
});

const WeatherIcon = ({ condition }: { condition: string }) => {
  if (condition === 'sunny') {
    return <Sunny style={{ width: 24, height: 24 }} />;
  }
  if (condition === 'cloudy') {
    return <Cloudy style={{ width: 24, height: 24 }} />;
  }
  if (condition === 'rainy') {
    return <Rainy style={{ width: 24, height: 24 }} />;
  }
  if (condition === 'partly cloudy') {
    return <PartlyCloudy style={{ width: 24, height: 24 }} />;
  }
  if (condition === 'partly sunny') {
    return <PartlySunny />;
  }
}

export default function CalendarList() {
  const [selectedDay, setSelectedDay] = useState(moment().format('YYYY-MM-DD'));
  const [currentPage, setCurrentPage] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get('window').width;
  const itemWidth = (screenWidth - 40) / 7;
  const { data: trips, refetch, isLoading } = getUpcomingTrips();
  const router = useRouter();
  const tripData = trips?.data?.events[0];
  console.log(tripData, 'tripData');

  const renderDayItem = ({ item: day, index }: { item: moment.Moment; index: number }) => (
    <Pressable
      key={day.format('YYYY-MM-DD')}
      onPress={() => setSelectedDay(day.format('YYYY-MM-DD'))}
      style={{ width: itemWidth }}>
      <DayItem
        selected={selectedDay === day.format('YYYY-MM-DD')}
        currentDay={day.format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')}>
        <Rect x={0} y={0} width={itemWidth} height={100}>
          <LinearGradient
            colors={gradientColors[index] as Color[]}
            start={vec(0, -60)}
            end={vec(itemWidth, 100)}
          />
        </Rect>
      </DayItem>
      <DayItemContent>
        <WeatherIcon condition={weatherData[index].condition} />
        <DayItemText>{day.format('DD')}</DayItemText>
        <DayItemTextSmall>{day.format('ddd')}</DayItemTextSmall>
        <DayItemTextSmall>{weatherData[index].temperature}°</DayItemTextSmall>
      </DayItemContent>
    </Pressable>
  );

  const onViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentPage(Math.floor(viewableItems[0].index / 7));
    }
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50
  };

  return (
    <>
      <CalendarListContainer>
        <FlatList
          ref={flatListRef}
          data={days}
          renderItem={renderDayItem}
          keyExtractor={(item) => item.format('YYYY-MM-DD')}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          snapToInterval={screenWidth - 40}
          decelerationRate="fast"
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          contentContainerStyle={{
            paddingHorizontal: 0,
          }}
          getItemLayout={(data, index) => ({
            length: itemWidth,
            offset: itemWidth * index,
            index,
          })}
        />
      </CalendarListContainer>
      <TouchableOpacity
        onPress={() => router.push(`/trip-overview/${tripData?._id}`)}
        style={{
          marginTop: 10,
          alignSelf: 'flex-end',
          marginRight: 10,
        }}>
        <LuggageSvg style={{ width: 24, height: 24 }} />
      </TouchableOpacity>
      <CalendarContent>
        <CalendarContentTitle>
          Worn as planned
        </CalendarContentTitle>
        <ComingSoonText>Coming Soon</ComingSoonText>
      </CalendarContent>
    </>
  );
}
