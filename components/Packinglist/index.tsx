import { View, Alert, StyleSheet, ScrollView, FlatList, SafeAreaView } from 'react-native';
import { useSyncPackingListWithCloset } from '@/methods/sync-utils';
import HeaderDashboard from '@/components/common/HeaderDashboardTrip';

import { useSession } from '@/config/ctx';
import { useRouter, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

import { getUserProfile } from '@/methods/users';
import TitleHeader from '@/components/common/TitleHeader';
import OverviewItem from '@/components/OverviewItem';

import Button from '@/components/common/Button';
import AddClothsModal from '@/components/AddClothsModal';
import AddClothsCategoryModal from '@/components/AddClothsCategoryModal';

// Import DraggableFlatList
import DraggableFlatList, {
  NestableDraggableFlatList,
  NestableScrollContainer,
} from 'react-native-draggable-flatlist';
import uuid from 'react-native-uuid';
import Input, { InputGhost } from '@/components/common/Input';
import { Text } from '@/components/Themed';
import TripCard from '@/components/TripCard';
import { EmptyState } from '@/components/Packinglist/emptyState';
import HeaderPage from '@/components/common/HeaderPage';
import {
  getTripById,
  getPackingList,
  updatePackingList,
} from '@/methods/trips';
import moment from 'moment';
import AddCategoryModal from '@/components/AddCategory';
import { SubtitleText } from '@/components/common/Text';
import EditClothsModal from '@/components/EditClothsModal';
import AddItemsModal from '@/components/AddItems';
import { OverviewItemText } from '@/components/OverviewItem/styles';
import { PlusIcon } from 'lucide-react-native';
import { OverviewItemContainer } from '@/components/OverviewItem/styles';
import TemplateListModal from '../TemplateListModal';


interface ClothesItem {
  _id: string;
  name: string;
  isActive: boolean;
  type: 'clothes' | 'category';
  quantity?: number;
  note?: string;
}

interface PackingList {
  _id: string;
  name: string;
  isActive: boolean;
  type: 'clothes' | 'category';
  showSubItems?: boolean;
  items?: ClothesItem[];
}

export const PackingList = ({id}: {id: string}) => {
  const { signOut } = useSession();
  const router = useRouter();
  const queryClient = useQueryClient(); // Initialize queryClient
  const { data: userProfile, isLoading } = getUserProfile();
  const { data: trip } = getTripById(id as string);

  const [isTemplateListModalVisible, setIsTemplateListModalVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null);


  const [isAddCategoryModalVisible, setIsAddCategoryModalVisible] =
    useState(false);
  console.log(trip, 'trip');

  const { data: packingLists } = getPackingList(id as string);

  const { mutate: updatePackingListMutation } = updatePackingList();
  const { invalidateClosetCache, removeItemFromPackingListMutation, forceRefreshAllCaches, hardRefresh } = useSyncPackingListWithCloset();

  const handleSelectTemplate = (template: any) => {

    //check if the template is already in the packing list
    const templateExists = packingList.some((item) => item.name === template.name);
    if (templateExists) {
      Alert.alert('Template already exists');
      return;
    }

    const newTemplate = {
      ...template,
      _id: uuid.v4(),
      name: template.name,
      isActive: false,
      type: 'category',
      items: template.items.map((item: any) => ({
        ...item,
        _id: uuid.v4(),
        quantity: 1,
        isActive: false,
      })),
    }
    //the template to the packing list
    const newPackingList = packingList.map((item) => ({ ...item })); // Deep copy of packing list

    // Remove duplicate items (ensures existing items aren't re-added)

    //add the template items to the packing list
    updatePackingListMutation({
      eventId: id as string,
      packingList: [...newPackingList, newTemplate],
    });

    setPackingList([...newPackingList, newTemplate]);
    setIsTemplateListModalVisible(false);
  };

  useEffect(() => {
    if (packingLists) {
      // Make sure packingList exists and is an array before trying to spread it
      if (packingLists?.data?.packingList &&
          Array.isArray(packingLists.data.packingList) &&
          packingLists.data.packingList.length > 0) {

        setPackingList([...packingLists.data.packingList]);
        setShowEmptyState(false);
      } else {
        // Keep the empty state if there's no valid data
        setShowEmptyState(true);
      }
    }
  }, [packingLists]);

  const [packingList, setPackingList] = useState<PackingList[]>([]);
  const [isAddClothsModalVisible, setIsAddClothsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [categoryName, setCategoryName] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showEmptyState, setShowEmptyState] = useState(true);
  const [isEditClothsModalVisible, setIsEditClothsModalVisible] =
    useState(false);
  const [selectedCloth, setSelectedCloth] = useState<any>(null);
  const [isAddItemsModalVisible, setIsAddItemsModalVisible] = useState(false);

  const [activeTab, setActiveTab] = useState('Packing List');

  // Memoize the packing list data
  const memoizedPackingList = useMemo(() => packingList, [packingList]);

  // Memoize the count of total items
  const countOnAllTotalItems = useMemo(() => {
    return memoizedPackingList.reduce((acc, item) => {
      if (item?.type === 'category') {
        return acc + (item.items?.length || 0);
      }
      return acc + 1;
    }, 0);
  }, [memoizedPackingList]);

  // Memoize the getallClothItemsId function
  const getallClothItemsId = useMemo(() => {
    return memoizedPackingList.reduce((acc: string[], item) => {
      if (item?.type === 'category' && item.items) {
        return acc.concat(item.items.map((subItem) => subItem._id));
      }
      return acc;
    }, []);
  }, [memoizedPackingList]);

  // Memoize the addDefaultCategory function
  const addDefaultCategory = useCallback(() => {
    const clothesCategory: PackingList = {
      _id: uuid.v4(),
      name: 'Clothes',
      isActive: false,
      type: 'category',
      showSubItems: true,
      items: [],
    };

    const shoesCategory: PackingList = {
      _id: uuid.v4(),
      name: 'Shoes',
      isActive: false,
      type: 'category',
      showSubItems: true,
      items: [],
    };

    updatePackingListMutation({
      eventId: id as string,
      packingList: [clothesCategory, shoesCategory],
    });
    
    setPackingList([clothesCategory, shoesCategory]);
    
  }, [id, updatePackingListMutation]);

  // Memoize the addCategory function
  const addCategory = useCallback((category: string) => {
    if (category === '') {
      Alert.alert('Please enter a category name');
      return;
    }

    const categoryExists = memoizedPackingList.some((item) => item.name === category);
    if (categoryExists) {
      Alert.alert('Category already exists');
      return;
    }

    const newCategory: PackingList = {
      _id: uuid.v4(),
      name: category,
      isActive: false,
      type: 'category',
      showSubItems: true,
      items: [],
    };

    updatePackingListMutation({
      eventId: id as string,
      packingList: [...memoizedPackingList, newCategory],
    });
    setPackingList((prev) => [...prev, newCategory]);
    setIsAddCategoryModalVisible(false);
    setSelectedItem(newCategory);
    setTimeout(() => {
      setIsModalVisible(true);
    }, 1000);
  }, [id, memoizedPackingList, updatePackingListMutation]);

  const handleAddItem = (items: any[]) => {
    const existingItems = items.filter((item) =>
      packingList.some((packingItem) => {
        return packingItem._id === item._id;
      }),
    );

    const existingItemsCategory = items.filter((item) =>
      packingList.some((packingItem) => {
        if (packingItem?.type === 'category') {
          return packingItem?.items?.some(
            (subItem) => subItem._id === item._id,
          );
        }
      }),
    );

    let filteredItems = items.filter(
      (item) =>
        !packingList.some((packingItem) => {
          if (packingItem?.type === 'category') {
            return packingItem?.items?.some(
              (subItem) => subItem._id === item._id,
            );
          }
          return packingItem._id === item._id;
        }),
    );

    setPackingList((prev) => {
      const filteredItemsNew = prev.map((item) => {
        const existingItem = existingItems.find(
          (existingItem) => existingItem._id === item._id,
        );

        let alreadAdded = false;

        if (item.type === 'category') {
          item.items = item.items?.map((subItem) => {
            const subItemExist = existingItemsCategory.find(
              (existingItem) => existingItem._id === subItem._id,
            );

            if (subItemExist) {
              alreadAdded = true;
              return { ...subItem, quantity: subItem.quantity + 1 };
            }
            return subItem;
          });
        }

        if (alreadAdded) {
          return item;
        }

        if (existingItem && !alreadAdded) {
          return { ...existingItem, quantity: item.quantity + 1 };
        }

        return item;
      });

      updatePackingListMutation({
        eventId: id as string,
        packingList: [...filteredItemsNew, ...filteredItems],
      });

      return [...filteredItemsNew, ...filteredItems];
    });
  };

  // New function to handle drag end
  const handleDragEnd = ({ data }: { data: any[] }) => {
    setPackingList(data);
  };

  const handleAddItemInPackingList = (items: any[]) => {
    if (!selectedItem || !items.length) return;

    // Log the items being added
    console.log('Adding items to packing list:', JSON.stringify(items, null, 2));

    let newPackingList = packingList.map((item) => ({ ...item })); // Deep copy of packing list

    // Remove duplicate items (ensures existing items aren't re-added)
    const packingListNew = newPackingList.filter(
      (item) => !items.some((newItem) => newItem._id === item._id),
    );

    // Find the category to add items into
    const selectedItemIndex = packingListNew.findIndex(
      (item) => item._id === selectedItem._id,
    );

    if (
      selectedItemIndex !== -1 &&
      Array.isArray(packingListNew[selectedItemIndex].items)
    ) {
      packingListNew[selectedItemIndex].items = [
        ...packingListNew[selectedItemIndex].items,
        ...items,
      ];
      packingListNew[selectedItemIndex].isActive = false;

      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        console.log('Forcing refetch of clothes data after adding items to packing list');
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    }

    setPackingList(packingListNew);
    updatePackingListMutation({
      eventId: id as string,
      packingList: packingListNew,
    });
  };

  const removeCategory = (category: any) => {
    Alert.alert(
      'Are you sure you want to delete this category?',
      'Deleting this category will remove these items from the packing list',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: () => {
            //get the category sub items and put them in the parent category
            const packingListNew = packingList.filter(
              (item) => item._id !== category._id,
            );
            //add the sub items to the parent category
            updatePackingListMutation({
              eventId: id as string,
              packingList: [...packingListNew],
            });
            setPackingList([...packingListNew]);

            // Invalidate the closet cache to ensure removed items are properly reflected
            invalidateClosetCache();
          },
        },
      ],
    );
  };

  const setShowSubItems = (categoryId: string, show: boolean) => {
    const packingListNew = packingList.map((item) => {
      if (item._id === categoryId) {
        item.showSubItems = show;
      }
      return item;
    });
    updatePackingListMutation({
      eventId: id as string,
      packingList: packingListNew,
    });
    setPackingList([...packingListNew]);
  };

  const changeAllActiveItems = (parentId: string, isActive: boolean) => {
    const newPackingList = packingList.map((item) => {
      return item;
    });

    const packingListNew = newPackingList.map((item) => {
      if (item._id === parentId) {
        item.isActive = isActive;
        item.items = item.items?.map((subItem) => {
          return { ...subItem, isActive: isActive };
        });
      }
      return item;
    });

    updatePackingListMutation({
      eventId: id as string,
      packingList: packingListNew,
    });
    setPackingList([...packingListNew]);
  };

  const changeItemsActive = (parentId: string, itemId: string) => {
    //toggle the active state of the item
    const newPackingList = packingList.map((item) => {
      return item;
    });

    const packingListNew = newPackingList.map((item) => {
      if (item._id === parentId) {
        item.items = item.items?.map((subItem) => {
          if (subItem._id === itemId) {
            console.log(!subItem.isActive, 'subItem------------------');
            return { ...subItem, isActive: !subItem.isActive };
          }
          return subItem;
        });
      }
      return item;
    });

    //check if all items are active in the category then set the category to active
    const allItemsActive = packingListNew
      .find((item) => item._id === parentId)
      ?.items?.every((subItem) => subItem.isActive);

    if (allItemsActive) {
      //find the index of the category
      const categoryIndex = packingListNew.findIndex(
        (item) => item._id === parentId,
      );
      if (categoryIndex !== -1) {
        packingListNew[categoryIndex].isActive = true;
      }
    } else {
      //find the index of the category
      const categoryIndex = packingListNew.findIndex(
        (item) => item._id === parentId,
      );
      if (categoryIndex !== -1) {
        packingListNew[categoryIndex].isActive = false;
      }
    }

    updatePackingListMutation({
      eventId: id as string,
      packingList: packingListNew,
    });
    setPackingList([...packingListNew]);

  };

  const removeItemOnCategory = (categoryId: string, itemSelected: any) => {
    //add Alert
    Alert.alert(
      'Are you sure you want to remove this item to the category?',
      'Some items will be removed from the packing list',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          onPress: () => {
            console.log(itemSelected, 'itemSelected');

            const packingListNew = new Array(...packingList);

            //remove the items inside the category
            packingListNew.forEach((item, itemIndex) => {
              console.log(item._id, categoryId, 'item -c ');
              if (item._id === categoryId) {
                //remove the item from the category
                const subItemIndex = packingListNew[itemIndex].items.findIndex(
                  (subItem) => subItem._id === itemSelected._id,
                );

                packingListNew[itemIndex].items.splice(subItemIndex, 1);
              }
              return item;
            });

            console.log(packingListNew, 'packingListNew');

            updatePackingListMutation({
              eventId: id as string,
              packingList: [...packingListNew, itemSelected],
            });
            setPackingList([...packingListNew, itemSelected]);

            // Invalidate the closet cache to ensure removed items are properly reflected
            invalidateClosetCache();
          },
        },
      ],
    );
  };

  const resetPackingList = () => {
    Alert.alert('Are you sure you want to reset the packing list?', '', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Reset',
        onPress: () => {
          setPackingList([]);
          updatePackingListMutation({ eventId: id as string, packingList: [] });
          setShowEmptyState(true);

          // Invalidate the closet cache to ensure removed items are properly reflected
          invalidateClosetCache();
        },
      },
    ]);
  };

  const removeItem = (itemId: string) => {
    Alert.alert('Are you sure you want to remove this item?', '', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Remove',
        onPress: () => {

          const newPackingList = packingList.map((item) => ({ ...item }));

          const packingListNew = newPackingList.filter(
            (item) => item._id !== itemId,
          );
          updatePackingListMutation({
            eventId: id as string,
            packingList: [...packingListNew],
          });
          setPackingList([...packingListNew]);

          // Remove the item from packing lists in the backend and invalidate caches
          removeItemFromPackingListMutation.mutate(itemId);

          // Force refresh all caches to ensure UI is updated
          setTimeout(() => {
            forceRefreshAllCaches();
            // As a last resort, perform a hard refresh after a delay
            setTimeout(hardRefresh, 2000);
          }, 1000);
        },
      },
    ]);
  };

  const removeSubItem = (parentId: string, subItemId: string) => {
    Alert.alert(
      'Are you sure you want to delete this Item?',
      'Deleting this item will remove it from your packing list but not from your virtual closet.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          onPress: () => {

            const newPackingList = packingList.map((item) => ({ ...item }));

            const packingListNew = newPackingList.map((item) => {
              if (item._id === parentId) {
                item.items = item.items?.filter(
                  (item) => item._id !== subItemId,
                );
              }
              return item;
            });
            updatePackingListMutation({
              eventId: id as string,
              packingList: [...packingListNew],
            });
            setPackingList([...packingListNew]);
            setIsEditClothsModalVisible(false);

            // Remove the item from packing lists in the backend and invalidate caches
            removeItemFromPackingListMutation.mutate(subItemId);

            // Force refresh all caches to ensure UI is updated
            setTimeout(() => {
              forceRefreshAllCaches();
              // As a last resort, perform a hard refresh after a delay
              setTimeout(hardRefresh, 2000);
            }, 1000);
          },
        },
      ],
    );
  };

  const saveEditCloths = (
    note: string,
    quantity: number,
    parentId: string,
    itemId: string,
    category: string,
    color: string,
    brand: string,
    imageUrl: string,
    name: string,
  ) => {
    let newPackingList = packingList.map((item) => ({ ...item })); // Deep copy of packing list

    //move the item to the new category

    const selectedItemIndex = newPackingList.findIndex(
      (item) => item._id === parentId,
    );

    if (selectedItemIndex !== -1) {
      newPackingList[selectedItemIndex].items = newPackingList[
        selectedItemIndex
      ].items?.map((item) =>
        item._id === itemId
          ? { ...item, note: note, quantity: quantity, color: color, brand: brand, imageUrl: imageUrl, name: name }
          : item,
      );
    }

    //move the item to the new category if its the same category do nothing
    if (category && category._id !== parentId) {
      const selectedCategoryIndex = newPackingList.findIndex(
        (item) => item._id === category._id,
      );

      if (selectedCategoryIndex !== -1) {
        //get the item from the old category
        const itemToMove = newPackingList[selectedItemIndex].items?.find(
          (item) => item._id === itemId,
        );

        newPackingList[selectedCategoryIndex].items = [
          ...newPackingList[selectedCategoryIndex].items,
          itemToMove,
        ];
      }

      //remove the item from the old category
      const oldCategoryIndex = newPackingList.findIndex(
        (item) => item._id === parentId,
      );

      if (oldCategoryIndex !== -1) {
        newPackingList[oldCategoryIndex].items = newPackingList[
          oldCategoryIndex
        ].items?.filter((item) => item._id !== itemId);
      }
    }
    setPackingList([...newPackingList]);
    updatePackingListMutation({
      eventId: id as string,
      packingList: newPackingList,
    });
  };

  return (
    <View style={{ flexGrow: 1, flexBasis: '100%' }}>
      <View style={{ flex: 1 }}>
          {showEmptyState && (
            <View style={{ marginTop: 32 }}>
              <EmptyState
                onPress={() => {
                  setShowEmptyState(false);
                  //set a default category add clothes and shoes
                  addDefaultCategory();
                }}
                onPressTemplate={() =>{
                  setIsTemplateListModalVisible(true)
                }}
              />
            </View>
          )}
          {!showEmptyState && (
            <ScrollView
              style={{
                flexGrow: 1,
                flexShrink: 0,
                flexBasis: '100%'
              }}
              contentContainerStyle={{ paddingBottom: 350 }}
              showsVerticalScrollIndicator={false}
            >
              <View style={{ marginTop: 32,}}>
                <View
                  style={{
                    marginBottom: 20,
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    gap: 10,
                  }}
                >
                  <TitleHeader title="My Packing List" />
                  <SubtitleText string={`(${0} / ${countOnAllTotalItems})`} />
                </View>
                <FlatList
                  data={memoizedPackingList}
                  keyExtractor={(item) => item._id}
                  renderItem={({ item }) => (
                    <View>
                      <OverviewItem
                        item={item}
                        changeAllActiveItems={changeAllActiveItems}
                        setShowSubItems={setShowSubItems}
                        removeCategory={removeCategory}
                        removeItem={removeItem}
                        onPress={() => {
                          if (item?.type === 'category') {
                            setSelectedItem(item);
                            setIsModalVisible(true);
                          }
                        }}
                      />
                      {item?.items &&
                      item?.items?.length > 0 &&
                      item?.showSubItems ? (
                        <FlatList
                          style={{ marginLeft: 20, marginBottom: 10 }}
                          scrollEnabled={false}
                          contentContainerStyle={{ gap: 10,  }}
                          data={[...item?.items, { addItem: true }]}
                          renderItem={({ item: subItem }) => (
                            <OverviewItem
                              changeItemsActive={changeItemsActive}
                              addCloths={() => {
                                if (item?.type === 'category') {
                                  setSelectedItem(item);
                                  setIsModalVisible(true);
                                }
                              }}
                              parentId={item._id}
                              item={subItem}
                              isActive={false}
                              isSubItem={true}
                              onPress={() => {
                                setSelectedItem(item);
                                setSelectedCloth(subItem);
                                setIsEditClothsModalVisible(true);
                              }}
                              onLongPress={() => {}}
                              removeItem={removeItem}
                              removeSubItem={removeSubItem}
                            />
                          )}
                        />
                      ) : (
                        item?.showSubItems && (
                          <OverviewItemContainer
                            onPress={() => {
                              if (item?.type === 'category') {
                                setSelectedItem(item);
                                setIsModalVisible(true);
                              }
                            }}
                          >
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                gap: 10,
                                marginLeft: 20,
                              }}
                            >
                              <PlusIcon size={20} color="#5C5C5C" />
                              <OverviewItemText
                                style={{ color: '#5C5C5C' }}
                                numberOfLines={1}
                                ellipsizeMode="tail"
                              >
                                Add new item
                              </OverviewItemText>
                            </View>
                          </OverviewItemContainer>
                        )
                      )}
                    </View>
                  )}
                />
              </View>
              <SafeAreaView >
                <View >
                  <Button
                    title="Add New Category"
                  onPress={() => setIsAddCategoryModalVisible(true)}
                />
              </View>
              </SafeAreaView>
            </ScrollView>
          )}
        <AddClothsModal
          isVisible={isAddClothsModalVisible}
          onClose={() => setIsAddClothsModalVisible(false)}
          onAdd={handleAddItem}
        />
      </View>

      <AddClothsCategoryModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        item={selectedItem}
        addedCloths={getallClothItemsId}
        packingList={packingList}
        onAdd={handleAddItemInPackingList}
        setIsAddItemsModalVisible={setIsAddItemsModalVisible}
      />

      <AddCategoryModal
        isVisible={isAddCategoryModalVisible}
        onClose={() => setIsAddCategoryModalVisible(false)}
        item={selectedItem}
        packingList={packingList}
        setIsTemplateListModalVisible={setIsTemplateListModalVisible}
        onAdd={addCategory}
      />
      <EditClothsModal
        isVisible={isEditClothsModalVisible}
        onClose={() => setIsEditClothsModalVisible(false)}
        item={selectedCloth}
        packingList={packingList}
        selectedItem={selectedItem}
        saveEditCloths={saveEditCloths}
        removeItem={removeSubItem}
      />
      <AddItemsModal
        isVisible={isAddItemsModalVisible}
        onClose={() => setIsAddItemsModalVisible(false)}
        item={selectedItem}
        packingList={packingList}
        onAdd={handleAddItemInPackingList}
        setIsModalVisible={setIsModalVisible}
        handleAddItemInPackingList={handleAddItemInPackingList}
      />
      <TemplateListModal
        isVisible={isTemplateListModalVisible}
        onClose={() => setIsTemplateListModalVisible(false)}
        setShowEmptyState={setShowEmptyState}
        onSelect={handleSelectTemplate}
      />
    </View>
  );
}
