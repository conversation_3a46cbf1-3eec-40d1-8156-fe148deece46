import React from 'react';
import { TodayContainer, TodayTag, TodayTagText, TodayText } from './styles';
import { useQuery } from '@tanstack/react-query';
import { getUpcomingTrips } from '@/methods/trips';
import moment from 'moment';

export default function TodayComponent() {
  const { data: trips, refetch, isLoading } = getUpcomingTrips();

  const today = new Date();
  const upcomingTrip = moment(trips?.data?.events[0]?.startDate).isAfter(today);


  //compute if there a trip that is upcoming this week
  const isUpcomingTrip = moment(trips?.data?.events[0]?.startDate).isAfter(today);

  return (
    <TodayContainer>
      <TodayText>Today</TodayText>
      <TodayTag>
        <TodayTagText>
          {isUpcomingTrip ? 'You have 1 trip this week!' : 'No trips this week!'}
        </TodayTagText>
      </TodayTag>
    </TodayContainer>
  );
}
